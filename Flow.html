<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Company Branch Warehouse Connector</title>
    <link rel="stylesheet" href="https://code.getmdl.io/1.3.0/material.indigo-pink.min.css" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap">
    <style>
        /* Custom CSS for layout and node styling */
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
            background: #f5f5f5; /* Light gray background */
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh; /* Full viewport height */
        }

        #canvas {
            width: 95vw; /* Responsive width */
            height: 85vh; /* Responsive height */
            position: relative;
            background: #ffffff; /* White canvas background */
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); /* Soft shadow */
            border-radius: 12px; /* Rounded corners for the canvas */
            overflow: hidden; /* Hide overflow content */
            border: 1px solid #e0e0e0; /* Light border */
        }

        .node {
            position: absolute;
            padding: 20px;
            border-radius: 8px;
            color: white;
            cursor: grab; /* Indicate draggable */
            text-align: center;
            font-weight: 600;
            min-width: 120px; /* Minimum width for nodes */
            display: flex;
            flex-direction: column; /* Allow content and .ep to stack */
            justify-content: center;
            align-items: center;
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.2); /* Node shadow */
            transition: all 0.2s ease-in-out; /* Smooth transitions for hover/active */
            z-index: 20; /* Ensure nodes are above connectors and canvas background */
        }

        .node:active {
            cursor: grabbing; /* Indicate active dragging */
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3); /* Enhanced shadow on active */
            transform: scale(1.02); /* Slightly larger on active */
        }

        /* Adjusted positions for better visibility */
        .company {
            background-color: #3f51b5; /* MDL Indigo */
            top: 15%; /* Adjusted */
            left: 10%; /* Adjusted */
        }

        .branch {
            background-color: #009688; /* MDL Teal */
            top: 40%; /* Adjusted */
            left: 15%; /* Adjusted */
        }

        .warehouse {
            background-color: #ff5722; /* MDL Deep Orange */
            top: 65%; /* Adjusted */
            left: 20%; /* Adjusted */
        }

        /* Additional nodes with adjusted positions */
        #company2 {
            top: 15%; /* Adjusted */
            left: 70%; /* Adjusted */
        }

        #branch2 {
            top: 40%; /* Adjusted */
            left: 65%; /* Adjusted */
        }

        #warehouse2 {
            top: 65%; /* Adjusted */
            left: 60%; /* Adjusted */
        }

        /* jsPlumb specific styles */
        .jtk-connector {
            z-index: 10; /* Ensure connectors are above nodes */
        }
        .jtk-endpoint {
            z-index: 11; /* Ensure endpoints are above connectors */
        }
        .dragHover {
            border: 2px dashed #424242; /* Dark gray dashed border on hover for target */
        }

        /* Style for the endpoint element within each node */
        .ep {
            width: 20px;
            height: 20px;
            background-color: rgba(255, 255, 255, 0.3); /* Slightly transparent white */
            border-radius: 50%; /* Circular shape */
            position: absolute; /* Position relative to the node */
            bottom: -10px; /* Position it slightly outside the bottom of the node */
            left: 50%;
            transform: translateX(-50%);
            cursor: crosshair; /* Indicates it's a connection point */
            border: 2px solid rgba(255, 255, 255, 0.6); /* White border */
            z-index: 25; /* Ensure it's above the node itself for interaction */
            transition: background-color 0.2s ease;
        }

        .ep:hover {
            background-color: rgba(255, 255, 255, 0.6); /* More opaque on hover */
        }

        /* Custom Message Box Styles */
        .message-box-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.6); /* Semi-transparent black overlay */
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000; /* Ensure it's on top of everything */
        }

        .message-box-content {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
            text-align: center;
            max-width: 400px;
            width: 90%;
            animation: fadeIn 0.3s ease-out; /* Simple fade-in animation */
        }

        .message-box-content p {
            font-size: 1.1em;
            color: #333333;
            margin-bottom: 25px;
        }

        .message-box-content button {
            background-color: #3f51b5; /* MDL Indigo */
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            transition: background-color 0.2s ease-in-out;
        }

        .message-box-content button:hover {
            background-color: #303f9f; /* Darker indigo on hover */
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <script src="https://code.getmdl.io/1.3.0/material.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsPlumb/2.15.6/js/jsplumb.min.js"></script>
</head>
<body>
    <div id="canvas">
        <div class="node company mdl-shadow--4dp" id="company1">Company 1 <div class="ep"></div></div>
        <div class="node company mdl-shadow--4dp" id="company2">Company 2 <div class="ep"></div></div>

        <div class="node branch mdl-shadow--4dp" id="branch1">Branch 1 <div class="ep"></div></div>
        <div class="node branch mdl-shadow--4dp" id="branch2">Branch 2 <div class="ep"></div></div>

        <div class="node warehouse mdl-shadow--4dp" id="warehouse1">Warehouse 1 <div class="ep"></div></div>
        <div class="node warehouse mdl-shadow--4dp" id="warehouse2">Warehouse 2 <div class="ep"></div></div>
    </div>

    <div id="messageBox" class="message-box-overlay hidden">
        <div class="message-box-content">
            <p id="messageText"></p>
            <button id="closeMessage">OK</button>
        </div>
    </div>

    <script>
        // Function to display custom messages (replaces alert())
        function showMessage(message) {
            const messageBox = document.getElementById('messageBox');
            const messageText = document.getElementById('messageText');
            messageText.textContent = message;
            messageBox.classList.remove('hidden'); // Show the message box
        }

        // Event listener for closing the custom message box
        document.getElementById('closeMessage').addEventListener('click', function() {
            document.getElementById('messageBox').classList.add('hidden'); // Hide the message box
        });

        jsPlumb.ready(function () {
            // Get the jsPlumb instance
            const instance = jsPlumb.getInstance({
                // Default connector style: Flowchart with rounded corners
                Connector: ['Flowchart', { cornerRadius: 5 }],
                // Endpoint style: simple Dot
                Endpoint: 'Dot',
                // Anchor points adjust dynamically to the closest edge of the node
                Anchor: 'Continuous',
                // Default paint style for connections (green, 3px width)
                PaintStyle: { stroke: '#4CAF50', strokeWidth: 3 },
                // Default style for endpoints (green fill, 6px radius)
                EndpointStyle: { fill: '#4CAF50', radius: 6 },
                // Overlays for connections: arrows at both start and end
                ConnectionOverlays: [
                    // Arrow at the source end (location 0)
                    ['Arrow', { location: 0, width: 15, length: 15, id: 'arrowStart', direction: -1 }],
                    // Arrow at the target end (location 1)
                    ['Arrow', { location: 1, width: 15, length: 15, id: 'arrowEnd' }]
                ],
                // The container element for jsPlumb elements
                Container: 'canvas'
            });

            // Get all node IDs dynamically from the DOM
            const nodeIds = Array.from(document.querySelectorAll('.node')).map(node => node.id);

            // Iterate over each node to make it draggable and connectable
            nodeIds.forEach(id => {
                // Make the node draggable within the 'canvas' container
                instance.draggable(id, {
                    containment: 'canvas'
                });

                // Configure the node as a source for new connections
                instance.makeSource(id, {
                    filter: '.ep', // Now using the .ep element inside the node as the drag source
                    anchor: 'Continuous', // Anchor points adjust dynamically
                    connectorStyle: { stroke: '#2196F3', strokeWidth: 2 }, // Blue connector style for new connections
                    maxConnections: -1, // Allows unlimited outgoing connections. Change to '1' for one-to-one outgoing.
                    // Callback for when max connections are reached (though -1 means unlimited)
                    onMaxConnections: function (info) {
                        showMessage("Maximum connections reached for " + info.elementId);
                    }
                });

                // Configure the node as a target for incoming connections
                instance.makeTarget(id, {
                    dropOptions: { hoverClass: 'dragHover' }, // Add 'dragHover' class when a connection is dragged over
                    anchor: 'Continuous', // Anchor points adjust dynamically
                    allowLoopback: false, // Prevent connecting a node to itself
                    maxConnections: -1 // Allows unlimited incoming connections. Change to '1' for one-to-one incoming.
                });
            });

            // Bind the 'beforeDrop' event to validate connections before they are established
            instance.bind("beforeDrop", function (info) {
                const sourceNode = document.getElementById(info.sourceId);
                const targetNode = document.getElementById(info.targetId);

                // Determine the type of the source and target nodes based on their class names
                const isSourceCompany = sourceNode.classList.contains("company");
                const isSourceBranch = sourceNode.classList.contains("branch");
                const isSourceWarehouse = sourceNode.classList.contains("warehouse");

                const isTargetCompany = targetNode.classList.contains("company");
                const isTargetBranch = targetNode.classList.contains("branch");
                const isTargetWarehouse = targetNode.classList.contains("warehouse");

                // Define valid connection rules:
                // 1. Company can connect to Branch
                if (isSourceCompany && isTargetBranch) {
                    return true;
                }
                // 2. Branch can connect to Warehouse
                if (isSourceBranch && isTargetWarehouse) {
                    return true;
                }

                // If none of the valid rules match, prevent the connection and show an error message
                showMessage("Invalid connection! Only Company to Branch and Branch to Warehouse connections are allowed.");
                return false; // Return false to prevent the connection from being made
            });

            // Repaint all connections when the window is resized to ensure layout correctness
            window.addEventListener('resize', instance.repaintEverything);
            // Also repaint everything after the initial setup to ensure all elements are correctly positioned
            instance.repaintEverything();
        })